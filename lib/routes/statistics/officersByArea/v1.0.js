const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê số cán bộ đang làm việc theo khu vực
 * POST /api/v1.0/statistics/officers-by-area
 *
 * T<PERSON><PERSON> về thống kê phân bố cán bộ đang hoạt động trong từng khu vực địa lý
 * Bao gồm số lượng cán bộ, tỷ lệ điểm danh và chi tiết theo đơn vị
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: error.details[0].message
        }
      });
    }

    next();
  };

  /**
   * Lấy thống kê cán bộ theo khu vực từ service với cache layer
   */
  const getOfficersByAreaStats = async (next) => {
    try {
      // Check cache trước
      const cachedData = await statisticsMetadataService.getCachedStatisticsData(
        'officers_by_area',
        timeRange
      );

      if (cachedData) {
        console.log('[Cache Hit] Officers by area stats from cache');
        result = {
          success: true,
          data: cachedData,
          fromCache: true,
          cacheTimestamp: Date.now()
        };
        return next();
      }

      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching officers by area stats from database');
      const serviceResult = await statisticsService.getOfficersByAreaStats({
        timeRange,
        startDate,
        endDate,
        userId
      });

      if (!serviceResult || !serviceResult.success) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
        });
      }

      // Cache kết quả
      try {
        await statisticsMetadataService.cacheStatisticsData(
          'officers_by_area',
          timeRange,
          serviceResult.data
        );
        console.log('[Cache Set] Cached officers by area stats');
      } catch (cacheError) {
        console.error('[Cache Error] Failed to cache officers by area stats:', cacheError);
      }

      result = {
        ...serviceResult,
        fromCache: false,
        cacheTimestamp: Date.now()
      };
      next();

    } catch (error) {
      console.error('[API Error] Error in getOfficersByAreaStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });

    // Ghi log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_officers_by_area_stats',
        description: 'Xem thống kê cán bộ theo khu vực',
        data: req.body,
        updatedData: {
          totalAreas: result.data?.summary?.totalAreas || 0,
          totalOfficers: result.data?.summary?.totalOfficers || 0,
          averageOfficersPerArea: result.data?.summary?.averageOfficersPerArea || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getOfficersByAreaStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
