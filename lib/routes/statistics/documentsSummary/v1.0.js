const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê tổng quan văn bản
 * POST /api/v1.0/statistics/documents-summary
 *
 * Trả về thống kê tổng quan về văn bản trong hệ thống
 * Bao gồm 3 loại văn bản chính: incoming (đến), outgoing (đi), reply (trả lời) và các loại văn bản phụ
 * Phân tích theo thời gian
 * Dữ liệu này đư<PERSON><PERSON> sử dụng để hiển thị dashboard quản lý văn bản
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Lấy thống kê tổng quan văn bản từ service với cache layer
   */
  const getDocumentsSummaryStats = async (next) => {
    try {
      // Check cache trước
      const cachedData = await statisticsMetadataService.getCachedStatisticsData(
        'documents_summary',
        timeRange
      );

      if (cachedData) {
        console.log('[Cache Hit] Documents summary stats from cache');
        result = {
          success: true,
          data: cachedData,
          fromCache: true,
          cacheTimestamp: Date.now()
        };
        return next();
      }

      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching documents summary stats from database');
      const serviceResult = await statisticsService.getDocumentsSummaryStats({
        timeRange,
        startDate,
        endDate,
        userId
      });

      if (!serviceResult || !serviceResult.success) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
        });
      }

      // Cache kết quả
      try {
        await statisticsMetadataService.cacheStatisticsData(
          'documents_summary',
          timeRange,
          serviceResult.data
        );
        console.log('[Cache Set] Cached documents summary stats');
      } catch (cacheError) {
        console.error('[Cache Error] Failed to cache documents summary stats:', cacheError);
      }

      result = {
        ...serviceResult,
        fromCache: false,
        cacheTimestamp: Date.now()
      };
      next();

    } catch (error) {
      console.error('[API Error] Error in getDocumentsSummaryStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_documents_summary_stats',
        description: 'Xem thống kê tổng quan văn bản',
        data: req.body,
        updatedData: {
          totalReports: result.data?.summary?.totalReports || 0,
          totalDocuments: result.data?.summary?.totalDocuments || 0,
          incomingDocuments: result.data?.summary?.incomingDocuments || 0,
          outgoingDocuments: result.data?.summary?.outgoingDocuments || 0,
          replyDocuments: result.data?.summary?.replyDocuments || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getDocumentsSummaryStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
