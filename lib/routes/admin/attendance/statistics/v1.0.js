const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const attendanceService = require('../../../../services/attendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API thống kê chuyên cần với hỗ trợ lọc theo trạng thái ca làm việc
 * POST /api/v1.0/attendance/statistics
 *
 * Tham số đầu vào:
 * - userId: ID cán bộ (optional)
 * - unitId: ID đơn vị (optional)
 * - startDate: <PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (DD-MM-YYYY, optional)
 * - endDate: <PERSON><PERSON><PERSON> kết thúc (DD-MM-YYYY, optional)
 * - status: Tr<PERSON>ng thái ca làm việc để lọc (optional)
 *   <PERSON><PERSON> thể là: string, array, hoặc comma-separated string
 *   Các giá trị hợp lệ: 'on_time', 'late', 'absent', 'excused', 'business_trip', 'nonattendance', 'no_schedule'
 *
 * Response bao gồm:
 * - Thông tin thống kê tổng quan
 * - Chi tiết thống kê từng cán bộ với currentShiftStatus
 * - Thông tin ca làm việc hiện tại
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    userId,
    unitId,
    startDate = DateUtils.getCurrentDateDDMMYYYY(),
    endDate = DateUtils.getCurrentDateDDMMYYYY(),
    status
  } = req.body;

  const validateParams = (next) => {
    // Valid shift status values
    const validStatuses = ['on_time', 'late', 'absent', 'excused', 'business_trip', 'nonattendance', 'no_schedule'];

    const schema = Joi.object({
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      status: Joi.alternatives().try(
        Joi.string().valid(...validStatuses),
        Joi.array().items(Joi.string().valid(...validStatuses)),
        Joi.string().custom((value, helpers) => {
          // Handle comma-separated values
          const statuses = value.split(',').map(s => s.trim());
          const invalidStatuses = statuses.filter(s => !validStatuses.includes(s));
          if (invalidStatuses.length > 0) {
            return helpers.error('any.invalid');
          }
          return statuses;
        })
      ).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate
    if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    next();
  };

  const getStatistics = (next) => {
    try {
      attendanceService.getAttendanceStatisticsAdmin(
        viewerId,
        userId,
        unitId,
        startDate,
        endDate
      )
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Apply status filter if provided
          let filteredData = res.data;
          if (status && res.data && res.data.statistics) {
            filteredData = {
              ...res.data,
              statistics: filterByStatus(res.data.statistics, status)
            };
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            // message: res.message,
            data: filteredData
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Filter statistics by shift status
   * @param {Array} statistics - Array of user statistics
   * @param {String|Array} statusFilter - Status filter (single value, array, or comma-separated string)
   * @returns {Array} Filtered statistics
   */
  const filterByStatus = (statistics, statusFilter) => {
    if (!statistics || !Array.isArray(statistics)) {
      return statistics;
    }

    // Normalize status filter to array
    let statusArray = [];
    if (typeof statusFilter === 'string') {
      if (statusFilter.includes(',')) {
        statusArray = statusFilter.split(',').map(s => s.trim());
      } else {
        statusArray = [statusFilter];
      }
    } else if (Array.isArray(statusFilter)) {
      statusArray = statusFilter;
    } else {
      return statistics; // Invalid filter, return all
    }

    // Filter statistics based on shift status
    return statistics.filter(userStat => {
      if (!userStat.currentShiftStatus || !userStat.currentShiftStatus.status) {
        // If no current shift status, include only if 'no_schedule' is in filter
        return statusArray.includes('no_schedule');
      }

      return statusArray.includes(userStat.currentShiftStatus.status);
    });
  };

  async.waterfall([
    validateParams,
    getStatistics
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};